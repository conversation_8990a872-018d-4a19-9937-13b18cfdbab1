/*
 * Created on Dec 15, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao.hibernate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Iterator;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.control.dao.UserProfilesDtlDAO;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.*;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.hibernate.SessionFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
@Repository("userProfilesDtlDAO")
@Transactional
public class UserProfilesDtlDAOHibernate extends CustomHibernateDaoSupport implements UserProfilesDtlDAO {
    
    private final Log log = LogFactory.getLog(UserProfilesDtlDAOHibernate.class);
    
    /**
     * Constructor with dependency injection
     * @param sessionfactory The session factory
     * @param entityManager The entity manager
     */
    public UserProfilesDtlDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }
    
    /**
     * Save window details for user profile
     * @param userprofilesdtl The user profile detail to save
     */
    public void savewindowdetails(UserProfileDetail userprofilesdtl) {
        log.debug("Inside the save window details");
        
        Session session = null;
        Transaction tx = null;
        
        try {
            session = getHibernateTemplate().getSessionFactory().withOptions().openSession();
            tx = session.beginTransaction();

            try {
                // Generate sequence number if not set (replaces the old UserProfileSequenceGenerator functionality)
                if (userprofilesdtl.getId().getSequenceNumber() == null) {
                    try {
                        Long sequenceValue = org.swallow.util.SequenceFactory.getSequenceFromDbAsLong("S_USER_PROFILE_DETAIL_SEQUENCE");
                        userprofilesdtl.getId().setSequenceNumber(sequenceValue);
                        log.debug("Generated new user profile detail sequence number: " + sequenceValue);
                    } catch (Exception e) {
                        log.error("Error generating user profile detail sequence", e);
                        throw new SwtException("Failed to generate user profile detail sequence", e);
                    }
                }

                userprofilesdtl.setMenuItem(null);
                session.save(userprofilesdtl);
                tx.commit();
            } catch (Exception e) {
                if (tx != null) tx.rollback();
                throw e;
            }
            
            log.debug("Outside the save window details");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in savewindowdetails: " + e.getMessage());
            try {
                throw SwtErrorHandler.getInstance().handleException(e, "savewindowdetails", this.getClass());
            } catch (Exception ex) {
                // Swallow exception as per original code
            }
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }
    
    /**
     * Update window details for user profile
     * @param userprofilesdtl The user profile detail to update
     */
    public void updatewindowdetails(UserProfileDetail userprofilesdtl) {
        log.debug("Inside the update window details");
        
        Session session = null;
        Transaction tx = null;
        
        try {
            SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            session = getHibernateTemplate().getSessionFactory()
                    .withOptions().interceptor(interceptor).openSession();
            tx = session.beginTransaction();
            
            try {
                session.update(userprofilesdtl);
                tx.commit();
            } catch (Exception e) {
                if (tx != null) tx.rollback();
                throw e;
            }
            
            log.debug("Outside the update window details");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in updatewindowdetails: " + e.getMessage());
            try {
                throw SwtErrorHandler.getInstance().handleException(e, "updatewindowdetails", this.getClass());
            } catch (Exception ex) {
                // Swallow exception as per original code
            }
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }
    
    /**
     * Delete window details for user profile
     * @param userprofilesdtl The user profile detail to delete
     */
    public void deletewindowdetails(UserProfileDetail userprofilesdtl) {
        Session session = null;
        Transaction tx = null;
        
        try {
            log.debug("Inside the delete window details");
            SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            session = getHibernateTemplate().getSessionFactory()
                    .withOptions().interceptor(interceptor).openSession();
            tx = session.beginTransaction();
            
            try {
                session.delete(userprofilesdtl);
                tx.commit();
            } catch (Exception e) {
                if (tx != null) tx.rollback();
                throw e;
            }
            
            log.debug("Outside the delete window details");
        } catch (Exception exp) {
            log.error(this.getClass().getName() + "- [deletewindowdetails] - Exception " + exp.getMessage());
            try {
                throw SwtErrorHandler.getInstance().handleException(exp,
                        "deletewindowdetails", UserProfilesDtlDAOHibernate.class);
            } catch (Exception e) {
                // Swallow exception as per original code
            }
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }
    
    /**
     * Fetch menu details for a user profile
     * @param hostId The host ID
     * @param userId The user ID
     * @param profileId The profile ID
     * @return List of menu item IDs
     */
    public List fetchMenuDetails(String hostId, String userId, String profileId) {
        log.debug("Inside the fetch details method");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<String> query = session.createQuery(
                "Select userprdtl.id.menuItemId from UserProfileDtl userprdtl " +
                "where userprdtl.id.hostId = :hostId and userprdtl.id.userId = :userId " +
                "and userprdtl.id.profileId = :profileId", 
                String.class);
            query.setParameter("hostId", hostId);
            query.setParameter("userId", userId);
            query.setParameter("profileId", profileId);
            List<String> userprofiledtllist = query.getResultList();
            
            log.debug("Outside the fetchMenuDetails.size() window details===========>" + userprofiledtllist.size());
            log.debug("Outside the fetchMenuDetails.toString() window details===========>" + userprofiledtllist.toString());
            return userprofiledtllist;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in fetchMenuDetails: " + e.getMessage());
            return List.of(); // Return empty list on error
        }
    }
    
    /**
     * Get profile details for a user
     * @param hostId The host ID
     * @param userId The user ID
     * @param profileId The profile ID
     * @return List of user profile details
     * @throws SwtException If an error occurs during retrieval
     */
    public List getProfileDetails(String hostId, String userId, String profileId) throws SwtException {
        log.debug("Inside the 'getProfileDetails' method");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<UserProfileDetail> query = session.createQuery(
                "from UserProfileDetail u where u.id.hostId = :hostId and u.id.userId = :userId " +
                "and u.id.profileId = :profileId", 
                UserProfileDetail.class);
            query.setParameter("hostId", hostId);
            query.setParameter("userId", userId);
            query.setParameter("profileId", profileId);
            List<UserProfileDetail> profileDetails = query.getResultList();
            
            log.debug("profile details====>" + profileDetails);
            return profileDetails;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in getProfileDetails: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getProfileDetails", this.getClass());
        }
    }
    
    /**
     * <pre>
     * START: Added by Arumugam on 15-Jul-2010 
     * Mantis issue-1177: Menu not populated on main screen when No Access on
     * Menu Item Work Flow Control 
     * </pre>
     */
    /**
     * This method gets the valid menu items from database for the given user
     * and returns the same
     * 
     * @param roleId The role ID
     * @return Collection of menu items
     * @throws SwtException If an error occurs during retrieval
     */
    public Collection getMenuList(String roleId) throws SwtException {
        // Variable to hold list of menu
        List<MenuItem> menuItems = new ArrayList<>();
        
        try {
            // log debug message
            log.debug("getMenuList entry");
            
            try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
                // Query to fetch menu
                String queryString = "SELECT ma.menuItem.itemId, " +
                        "ma.menuItem.description, " +
                        "ma.menuItem.parentId, ma.menuItem.menuOrder, " +
                        "ma.menuItem.menuGroupOrder, " +
                        "ma.menuItem.imageName, ma.menuItem.width, " +
                        "ma.menuItem.height, ma.accessId, " +
                        "ma.menuItem.program.programName " +
                        "from MenuAccess ma " +
                        "where ma.id.roleId = :roleId " +
                        "ORDER BY TO_NUMBER(ma.menuItem.parentId), " +
                        "ma.menuItem.menuGroupOrder, ma.menuItem.menuOrder";
                
                TypedQuery<Object[]> query = session.createQuery(queryString, Object[].class);
                query.setParameter("roleId", roleId);
                List<Object[]> results = query.getResultList();
                
                // log debug message
                log.debug("Process result set");
                
                // Convert result to MenuItem objects
                for (Object[] record : results) {
                    int paramIndex = 0;
                    
                    // Initialize new menu item object
                    MenuItem menuItem = new MenuItem();
                    
                    // Set menu item id
                    menuItem.setItemId((String) record[paramIndex++]);
                    
                    // Set menu item name (description)
                    menuItem.setDescription((String) record[paramIndex++]);
                    
                    // Set parent menu item id
                    menuItem.setParentId((String) record[paramIndex++]);
                    
                    // Set menu item order
                    menuItem.setMenuOrder((Integer) record[paramIndex++]);
                    
                    // Set menu item group order
                    menuItem.setMenuGroupOrder((Integer) record[paramIndex++]);
                    
                    // Set image name(menu icon)
                    menuItem.setImageName((String) record[paramIndex++]);
                    
                    // Set screen width
                    menuItem.setWidth((Integer) record[paramIndex++]);
                    
                    // Set screen height
                    menuItem.setHeight((Integer) record[paramIndex++]);
                    
                    // Set access id (access right)
                    menuItem.setAccessId((String) record[paramIndex++]);
                    
                    // Set program name (perform action on click of menu item)
                    menuItem.setProgramName((String) record[paramIndex++]);
                    
                    // Add menu item object
                    menuItems.add(menuItem);
                }
            }
            
            // log debug message
            log.debug("Got menu items: " + menuItems.size());
            return menuItems;
        } catch (Exception ex) {
            log.error("Exception in getMenuList - " + ex.getMessage(), ex);
            return List.of(); // Return empty list on error
        } finally {
            // log debug message
            log.debug("getMenuList exit");
        }
    }
    /**
     * END: Added by Arumugam on 15-Jul-2010 for mantis issue-1177
     */
}