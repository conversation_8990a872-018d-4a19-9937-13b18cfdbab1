<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.model.UserProfileDetail" table="S_USER_PROFILE_DETAIL">
<!--   <id name="id" access="field" type="org.swallow.model.UserProfileDetailIdType"> -->
<!--    <column name="HOST_ID" not-null="true" sql-type="hostId"/> -->
<!--    <column name="USER_ID" not-null="true" sql-type="userId"/> -->
<!--    <column name="PROFILE_ID" not-null="true" sql-type="profileId"/> -->
<!--    <column name="MENU_ITEM_ID" not-null="true" sql-type="menuItemId"/> -->
<!--    <column name="SEQ_NUMBER" not-null="true" sql-type="sequenceNumber"/> -->
<!-- <generator class="org.swallow.model.UserProfileSequenceGenerator"> -->
<!--    </generator> -->
<!--   </id> -->

     <composite-id class="org.swallow.model.UserProfileDetail$Id" name="id" unsaved-value="any">
         <key-property name="hostId" access="field" column="HOST_ID"/>
         <key-property name="userId" access="field" column="USER_ID"/>
         <key-property name="profileId" access="field" column="PROFILE_ID"/>
         <key-property name="menuItemId" access="field" column="MENU_ITEM_ID"/>
         <key-property name="sequenceNumber" access="field" column="SEQ_NUMBER"/>
     </composite-id>


<property name="leftX" column="LEFT_X"/>
  <property name="leftY" column="LEFT_Y"/>
  <property name="width" column="WIDTH"/>
  <property name="height" column="HEIGHT"/>
  <property name="updateDate" column="UPDATE_DATE"/>
  <property name="updateUser" column="UPDATE_USER"/>
  <many-to-one name="userProfile" class="org.swallow.model.UserProfile" lazy="false"
   not-null="true" outer-join="true" update="false" insert="false" foreign-key="FK_S_USER_PROF_DTL_S_USER_PROF">
   <column name="HOST_ID"/>
   <column name="USER_ID"/>
   <column name="PROFILE_ID"/>
  </many-to-one>
  <many-to-one name="menuItem" class="org.swallow.model.MenuItem" lazy="false"
   column="MENU_ITEM_ID"  not-null="false" outer-join="true"
   update="false" insert="false" foreign-key="FK_S_USER_PRO_DTL_P_MENU_ITEM"/>
 </class>
</hibernate-mapping>
