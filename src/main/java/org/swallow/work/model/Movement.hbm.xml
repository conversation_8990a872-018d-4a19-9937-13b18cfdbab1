<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.work.model.Movement" table="P_MOVEMENT">
	   <composite-id name="id" class="org.swallow.work.model.Movement$Id" unsaved-value="any">
		   <key-property name="hostId" access="field" column="HOST_ID"/>
		   <key-property name="entityId" access="field" column="ENTITY_ID" />
		   <key-property name="movementId" access="field" column="MOVEMENT_ID"/>
	   </composite-id>
		<property name="currencyCode" column="CURRENCY_CODE" not-null="true"/>
		<property name="bookCode" column="BOOKCODE" not-null="false"/>		
		<property name="valueDate" column="VALUE_DATE" not-null="false"/>	
		<property name="amount" column="AMOUNT" not-null="true"/>
		<property name="sign" column="SIGN" not-null="true"/>
		<property name="movementType" column="MOVEMENT_TYPE" not-null="false"/>		
		<property name="accountId" column="ACCOUNT_ID" not-null="false"/>	
		<property name="reference1" column="REFERENCE1" not-null="false"/>	
		<property name="reference2" column="REFERENCE2" not-null="false"/>
		<property name="reference3" column="REFERENCE3" not-null="false"/>
		<property name="reference4" column="REFERENCE4" not-null="false"/>		
		<property name="counterPartyId" column="COUNTERPARTY_ID" not-null="false"/>	
		<property name="counterPartyText1" column="COUNTERPARTY_TEXT1" not-null="false"/>	
		<property name="counterPartyText2" column="COUNTERPARTY_TEXT2" not-null="false"/>
		<property name="counterPartyText3" column="COUNTERPARTY_TEXT3" not-null="false"/>
		<property name="counterPartyText4" column="COUNTERPARTY_TEXT4" not-null="false"/>		
		<property name="counterPartyText5" column="COUNTERPARTY_TEXT5" not-null="false"/>		
		<property name="beneficiaryId" column="BENEFICIARY_ID" not-null="false"/>	
		<property name="beneficiaryText1" column="BENEFICIARY_TEXT1" not-null="false"/>	
		<property name="beneficiaryText2" column="BENEFICIARY_TEXT2" not-null="false"/>
		<property name="beneficiaryText3" column="BENEFICIARY_TEXT3" not-null="false"/>
		<property name="beneficiaryText4" column="BENEFICIARY_TEXT4" not-null="false"/>		
		<property name="beneficiaryText5" column="BENEFICIARY_TEXT5" not-null="false"/>		
		<property name="custodianId" column="CUSTODIAN_ID" not-null="false"/>	
		<property name="custodianText1" column="CUSTODIAN_TEXT1" not-null="false"/>	
		<property name="custodianText2" column="CUSTODIAN_TEXT2" not-null="false"/>
		<property name="custodianText3" column="CUSTODIAN_TEXT3" not-null="false"/>
		<property name="custodianText4" column="CUSTODIAN_TEXT4" not-null="false"/>		
		<property name="custodianText5" column="CUSTODIAN_TEXT5" not-null="false"/>		
		<property name="bookCodeAvail" column="BOOKCODE_AVAIL" not-null="false"/>	
		<property name="positionLevel" column="POSITION_LEVEL" not-null="false"/>	
		<property name="predictStatus" column="PREDICT_STATUS" not-null="false"/>
		<property name="extractStatus" column="EXTRACT_STATUS" not-null="false"/>
		<property name="matchId" column="MATCH_ID" not-null="false"/>		
		<property name="matchStatus" column="MATCH_STATUS" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>
		<property name="inputDate" column="INPUT_DATE" not-null="false"/>
		<property name="inputSource" column="INPUT_SOURCE" not-null="false"/>		
		<property name="messageId" column="MESSAGE_ID" not-null="false"/>	
		<property name="messageFormat" column="MESSAGE_FORMAT" not-null="false"/>
		<property name="initialPredStatus" column="INITIAL_PREDICT_STATUS" not-null="false"/>	
		<property name="inputUser" column="INPUT_USER" not-null="false"/>	
		<property name="inputRole" column="INPUT_ROLE" not-null="false"/>
		<!-- Start: Refer to Smart-Predict_SRS_Open_Movements_0.2.doc -->
		<!-- <property name="origValueDate" column="ORIG_VALUE_DATE" not-null="false"/> -->
		<!-- End: Refer to Smart-Predict_SRS_Open_Movements_0.2.doc -->

		<property name="notesCount" column="NOTES_COUNT" not-null="false"/>
		<!-- Start: Refer to Smart-Predict_SRS_Open_Movements_0.2.doc -->
		<property name="openFlag" column="OPEN" not-null="true"/>
		<!-- End: Refer to Smart-Predict_SRS_Open_Movements_0.2.doc -->
		
		<!-- START: Code changed as par SRS - External Balance Calculation for ING, 11-JUL-2007 -->
		<property name="extBalStatus" column="EXT_BAL_STATUS" not-null="false"/>
		<!-- END: Code changed as par SRS - External Balance Calculation for ING, 11-JUL-2007 -->
		
		<!-- START: Refer to SRS_matching_improvement_a_new_strategy_0.2_Comments_MS_DJB.doc 18-SEP-2007 -->
		<property name="toMatch" column="TO_MATCH" not-null="false"/>
		<!-- END: Refer to SRS_matching_improvement_a_new_strategy_0.2_Comments_MS_DJB.doc 18-SEP-2007 -->
		<!-- START:code added by Mahesh on 23-Oct-2009 for Mantis 1034:  added new fields Matching party, Product Type, Posting date in UI-->
		<property name="matchingParty" column="MATCHING_PARTY" not-null="false"/>
		<property name="productType" column="PRODUCT_TYPE" not-null="false"/>
		<property name="postingDate" column="POSTING_DATE" not-null="false"/>
		<!-- END:code added by Mahesh on 23-Oct-2009 for Mantis 1034:  added new fields Matching party, Product Type, Posting date in UI-->
		<!-- Start: Add by Med Amine for Mantis 2352 :IntraDay Liquidity Monitoring and Analysis  -->
		<property name="settlementDateTime" column="SETTLEMENT_DATETIME" not-null="false"/>
		<property name="expectedSettlementDateTime" column="EXPECTED_SETTLEMENT_DATETIME" not-null="false"/>
		<property name="criticalPaymentType" column="CRITICAL_PAYMENT_TYPE" not-null="false"/>
		<!-- End code: IntraDay Liquidity Monitoring and Analysis  -->
		<property name="orderingCustomerId" column="ORDERING_CUSTOMER" not-null="false"/>
		<property name="orderingInstitutionId" column="ORDERING_INSTITUTION" not-null="false"/>
		<property name="senderCorrespondentId" column="SENDERS_CORRES" not-null="false"/>
		<property name="receiverCorrespondentId" column="RECEIVERS_CORRES" not-null="false"/>
		<property name="intermediaryInstitutionId" column="INTMDRY_INSTITUTION_ID" not-null="false"/>
		<property name="accountWithInstitutionId" column="ACC_WITH_INSTITUTION_ID" not-null="false"/>
		<property name="beneficiaryCustomerId" column="BENEFICIARY_CUST" not-null="false"/>
    
<property name="ilmFcastStatus" column="ILM_FCAST_STATUS" not-null="false"
/>
		<property name="uetr" column="UETR" not-null="false"/>	
    </class>
</hibernate-mapping>
